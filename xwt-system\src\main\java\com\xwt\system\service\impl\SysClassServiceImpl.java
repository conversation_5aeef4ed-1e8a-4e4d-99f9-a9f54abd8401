package com.xwt.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.xwt.common.utils.StringUtils;
import com.xwt.system.mapper.SysClassMapper;
import com.xwt.system.domain.SysClass;
import com.xwt.system.service.ISysClassService;

/**
 * 班级信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-10
 */
@Service
public class SysClassServiceImpl implements ISysClassService 
{
    @Autowired
    private SysClassMapper sysClassMapper;

    /**
     * 查询班级信息
     * 
     * @param classId 班级信息主键
     * @return 班级信息
     */
    @Override
    public SysClass selectSysClassByClassId(Long classId)
    {
        return sysClassMapper.selectSysClassByClassId(classId);
    }

    /**
     * 查询班级信息列表
     * 
     * @param sysClass 班级信息
     * @return 班级信息
     */
    @Override
    public List<SysClass> selectSysClassList(SysClass sysClass)
    {
        return sysClassMapper.selectSysClassList(sysClass);
    }

    /**
     * 新增班级信息
     * 
     * @param sysClass 班级信息
     * @return 结果
     */
    @Override
    public int insertSysClass(SysClass sysClass)
    {
        return sysClassMapper.insertSysClass(sysClass);
    }

    /**
     * 修改班级信息
     * 
     * @param sysClass 班级信息
     * @return 结果
     */
    @Override
    public int updateSysClass(SysClass sysClass)
    {
        return sysClassMapper.updateSysClass(sysClass);
    }

    /**
     * 批量删除班级信息
     * 
     * @param classIds 需要删除的班级信息主键
     * @return 结果
     */
    @Override
    public int deleteSysClassByClassIds(Long[] classIds)
    {
        return sysClassMapper.deleteSysClassByClassIds(classIds);
    }

    /**
     * 删除班级信息信息
     * 
     * @param classId 班级信息主键
     * @return 结果
     */
    @Override
    public int deleteSysClassByClassId(Long classId)
    {
        return sysClassMapper.deleteSysClassByClassId(classId);
    }

    /**
     * 校验班级编码是否唯一
     * 
     * @param sysClass 班级信息
     * @return 结果
     */
    @Override
    public boolean checkClassCodeUnique(SysClass sysClass)
    {
        Long classId = StringUtils.isNull(sysClass.getClassId()) ? -1L : sysClass.getClassId();
        SysClass info = sysClassMapper.checkClassCodeUnique(sysClass.getClassCode());
        if (StringUtils.isNotNull(info) && info.getClassId().longValue() != classId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 校验班级名称是否唯一
     * 
     * @param sysClass 班级信息
     * @return 结果
     */
    @Override
    public boolean checkClassNameUnique(SysClass sysClass)
    {
        Long classId = StringUtils.isNull(sysClass.getClassId()) ? -1L : sysClass.getClassId();
        SysClass info = sysClassMapper.checkClassNameUnique(sysClass.getClassName());
        if (StringUtils.isNotNull(info) && info.getClassId().longValue() != classId.longValue())
        {
            return false;
        }
        return true;
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xwt.system.mapper.SysClassMapper">
    
    <resultMap type="SysClass" id="SysClassResult">
        <result property="classId"          column="class_id"          />
        <result property="className"        column="class_name"        />
        <result property="classCode"        column="class_code"        />
        <result property="deptId"           column="dept_id"           />
        <result property="grade"            column="grade"             />
        <result property="major"            column="major"             />
        <result property="headTeacherId"    column="head_teacher_id"   />
        <result property="studentCount"     column="student_count"     />
        <result property="status"           column="status"            />
        <result property="createBy"         column="create_by"         />
        <result property="createTime"       column="create_time"       />
        <result property="updateBy"         column="update_by"         />
        <result property="updateTime"       column="update_time"       />
        <result property="deptName"         column="dept_name"         />
        <result property="headTeacherName"  column="head_teacher_name" />
    </resultMap>

    <sql id="selectSysClassVo">
        select c.class_id, c.class_name, c.class_code, c.dept_id, c.grade, c.major, 
               c.head_teacher_id, c.student_count, c.status, c.create_by, c.create_time, 
               c.update_by, c.update_time,
               d.dept_name,
               u.nick_name as head_teacher_name
        from sys_class c
        left join sys_dept d on c.dept_id = d.dept_id
        left join sys_user u on c.head_teacher_id = u.user_id
    </sql>

    <select id="selectSysClassList" parameterType="SysClass" resultMap="SysClassResult">
        <include refid="selectSysClassVo"/>
        <where>  
            <if test="className != null  and className != ''"> and c.class_name like concat('%', #{className}, '%')</if>
            <if test="classCode != null  and classCode != ''"> and c.class_code = #{classCode}</if>
            <if test="deptId != null "> and c.dept_id = #{deptId}</if>
            <if test="grade != null  and grade != ''"> and c.grade = #{grade}</if>
            <if test="major != null  and major != ''"> and c.major like concat('%', #{major}, '%')</if>
            <if test="headTeacherId != null "> and c.head_teacher_id = #{headTeacherId}</if>
            <if test="status != null  and status != ''"> and c.status = #{status}</if>
        </where>
        order by c.create_time desc
    </select>
    
    <select id="selectSysClassByClassId" parameterType="Long" resultMap="SysClassResult">
        <include refid="selectSysClassVo"/>
        where c.class_id = #{classId}
    </select>
        
    <insert id="insertSysClass" parameterType="SysClass" useGeneratedKeys="true" keyProperty="classId">
        insert into sys_class
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="className != null and className != ''">class_name,</if>
            <if test="classCode != null and classCode != ''">class_code,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="grade != null">grade,</if>
            <if test="major != null">major,</if>
            <if test="headTeacherId != null">head_teacher_id,</if>
            <if test="studentCount != null">student_count,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="className != null and className != ''">#{className},</if>
            <if test="classCode != null and classCode != ''">#{classCode},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="grade != null">#{grade},</if>
            <if test="major != null">#{major},</if>
            <if test="headTeacherId != null">#{headTeacherId},</if>
            <if test="studentCount != null">#{studentCount},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateSysClass" parameterType="SysClass">
        update sys_class
        <trim prefix="SET" suffixOverrides=",">
            <if test="className != null and className != ''">class_name = #{className},</if>
            <if test="classCode != null and classCode != ''">class_code = #{classCode},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="grade != null">grade = #{grade},</if>
            <if test="major != null">major = #{major},</if>
            <if test="headTeacherId != null">head_teacher_id = #{headTeacherId},</if>
            <if test="studentCount != null">student_count = #{studentCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </trim>
        where class_id = #{classId}
    </update>

    <delete id="deleteSysClassByClassId" parameterType="Long">
        delete from sys_class where class_id = #{classId}
    </delete>

    <delete id="deleteSysClassByClassIds" parameterType="String">
        delete from sys_class where class_id in 
        <foreach item="classId" collection="array" open="(" separator="," close=")">
            #{classId}
        </foreach>
    </delete>

    <select id="checkClassCodeUnique" parameterType="String" resultMap="SysClassResult">
        <include refid="selectSysClassVo"/>
        where c.class_code = #{classCode} limit 1
    </select>

    <select id="checkClassNameUnique" parameterType="String" resultMap="SysClassResult">
        <include refid="selectSysClassVo"/>
        where c.class_name = #{className} limit 1
    </select>
</mapper>
